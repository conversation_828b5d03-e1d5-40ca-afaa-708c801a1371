package com.lc.billion.icefire.game.benchmark;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.simfun.sgf.thread2.SingleThreadDisruptorTaskWorker;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * JMH 基准测试：SingleThreadTaskWorker vs SingleThreadDisruptorTaskWorker
 * 
 * 使用 JMH (Java Microbenchmark Harness) 进行专业的微基准测试
 * 
 * 运行方式：
 * 1. Maven: mvn test-compile exec:exec -Dexec.mainClass="org.openjdk.jmh.Main" -Dexec.args="WorkerJMHBenchmark"
 * 2. IDE: 直接运行 main 方法
 * 3. JMH插件: mvn jmh:run
 *
 */
@BenchmarkMode(Mode.Throughput)  // 测量吞吐量
@OutputTimeUnit(TimeUnit.SECONDS)  // 输出单位：每秒
@State(Scope.Benchmark)  // 基准测试级别的状态
@Warmup(iterations = 4, time = 1, timeUnit = TimeUnit.SECONDS)  // 预热：2次，每次1秒
@Measurement(iterations = 4, time = 1, timeUnit = TimeUnit.SECONDS)  // 测量：3次，每次2秒
@Fork(value = 1, jvmArgsAppend = {"-Xmx4g", "-XX:+UseZGC"})  // 1个fork，使用ZGC
@Threads(1)
public class WorkerJMHBenchmark2 {

    // 测试参数：提交线程数
    @Param({"8", "16"})
    private int submitterThreads;
    
    // 测试参数：每个线程提交的消息数
    @Param({"1000"})
    private int messagesPerThread;

    // 总消息数
    private int messageCount;

    // 测试状态变量
    private TestSingleThreadTaskWorker singleThreadWorker;
    private SingleThreadDisruptorTaskWorker<TestMessage> disruptorWorker;
    private TestMessage[] messages;

    // 测试消息类
    static class TestMessage {
        private final String name;
        private final long timestamp;
        private final String payload;
        
        public TestMessage(String name, String payload) {
            this.name = name;
            this.timestamp = System.nanoTime();
            this.payload = payload;
        }
        
        public String getName() {
            return name;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public String getPayload() {
            return payload;
        }
    }

    // SingleThreadTaskWorker 测试实现
    static class TestSingleThreadTaskWorker extends com.simfun.sgf.compat.SingleThreadTaskWorker<TestMessage> {
        private final AtomicInteger processedCount = new AtomicInteger();
        private final AtomicLong totalLatency = new AtomicLong();
        private volatile boolean running = true;
        
        @Override
        protected void execute(TestMessage task, Object... attach) {
            // 计算延迟
            long latency = System.nanoTime() - task.getTimestamp();
            totalLatency.addAndGet(latency);
            // 模拟少量工作（避免影响基准测试准确性）
            Blackhole.consumeCPU(10);
            processedCount.incrementAndGet();
        }
        
        public void submitTask(TestMessage message) {
            addTask(message);
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
        
        public void reset() {
            processedCount.set(0);
            totalLatency.set(0);
            running = true;
        }
        
        public void shutdown() {
            running = false;
            super.stop();
        }
    }

    // SingleThreadDisruptorTaskWorker 测试实现
    static class TestDisruptorTaskWorker extends SingleThreadDisruptorTaskWorker<TestMessage> {
        private final AtomicInteger processedCount = new AtomicInteger();
        private final AtomicLong totalLatency = new AtomicLong();
        
        // 无参构造函数 - Builder反射调用需要
        public TestDisruptorTaskWorker() {
        }
        
        @Override
        protected void execute(TestMessage task, Object... attach) {
            // 计算延迟
            long latency = System.nanoTime() - task.getTimestamp();
            totalLatency.addAndGet(latency);
            // 模拟少量工作（避免影响基准测试准确性）
            Blackhole.consumeCPU(10);
            
            processedCount.incrementAndGet();
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
        
        public void reset() {
            processedCount.set(0);
            totalLatency.set(0);
        }
        
        public void submitTask(TestMessage message) {
            addTask(message);
        }
    }

    @Setup(Level.Trial)  // 每个试验开始前执行
    public void setupTrial() {
        // 设置兼容性消息数量
        messageCount = submitterThreads * messagesPerThread;
        
        // 创建测试消息
        messages = new TestMessage[messageCount];
        for (int i = 0; i < messageCount; i++) {
            messages[i] = new TestMessage("test-" + i, "payload-" + i);
        }
    }

    @Setup(Level.Iteration)  // 每次迭代开始前执行
    public void setupIteration() {
        // 初始化SingleThreadTaskWorker
        singleThreadWorker = new TestSingleThreadTaskWorker();
        singleThreadWorker.start();
        
        // 初始化DisruptorTaskWorker
        disruptorWorker = SingleThreadDisruptorTaskWorker
                .<TestMessage>newBuilder(TestDisruptorTaskWorker.class)
                .setName("BenchmarkDisruptorWorker")
                .setBufferSize(16384)
                .setWaitStrategy(new BlockingWaitStrategy())
                .build();
        
        if (disruptorWorker != null) {
            disruptorWorker.start();
        }
    }

    @TearDown(Level.Iteration)  // 每次迭代结束后执行
    public void tearDownIteration() {
        if (singleThreadWorker != null) {
            singleThreadWorker.shutdown();
        }
        if (disruptorWorker != null) {
            disruptorWorker.stop();
        }
    }

    /**
     * 基准测试：SingleThreadTaskWorker 消息处理吞吐量 (单线程提交场景)
     */
    //@Benchmark  // 暂时注释掉，专注多线程测试
    public long benchmarkSingleThreadTaskWorker(Blackhole blackhole) throws InterruptedException {
        singleThreadWorker.reset();
        
        // 提交所有消息
        for (TestMessage message : messages) {
            singleThreadWorker.submitTask(message);
        }
        
        // 等待处理完成
        long startTime = System.nanoTime();
        while (singleThreadWorker.getProcessedCount() < messageCount) {
            if (System.nanoTime() - startTime > TimeUnit.SECONDS.toNanos(10)) {
                throw new RuntimeException("SingleThreadTaskWorker 处理超时");
            }
            Thread.yield();
        }
        
        int processed = singleThreadWorker.getProcessedCount();
        blackhole.consume(processed);
        return processed;
    }

    /**
     * 基准测试：SingleThreadDisruptorTaskWorker 消息处理吞吐量 (单线程提交场景)
     */
    //@Benchmark  // 暂时注释掉，专注多线程测试
    public long benchmarkDisruptorTaskWorker(Blackhole blackhole) throws InterruptedException {
        if (disruptorWorker == null) {
            return 0;
        }
        
        ((TestDisruptorTaskWorker) disruptorWorker).reset();
        
        // 提交所有消息
        for (TestMessage message : messages) {
            ((TestDisruptorTaskWorker) disruptorWorker).submitTask(message);
        }
        
        // 等待处理完成
        long startTime = System.nanoTime();
        while (((TestDisruptorTaskWorker) disruptorWorker).getProcessedCount() < messageCount) {
            if (System.nanoTime() - startTime > TimeUnit.SECONDS.toNanos(10)) {
                throw new RuntimeException("DisruptorTaskWorker 处理超时");
            }
            Thread.yield();
        }
        
        int processed = ((TestDisruptorTaskWorker) disruptorWorker).getProcessedCount();
        blackhole.consume(processed);
        return processed;
    }

    /**
     * 多线程提交基准测试：SingleThreadTaskWorker
     * 模拟真实场景：多个线程同时提交，单线程执行
     */
    @Benchmark
    public long benchmarkSingleThreadTaskWorkerMultiSubmit(Blackhole blackhole) throws InterruptedException {
        singleThreadWorker.reset();
        
        int totalMessages = submitterThreads * messagesPerThread;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(submitterThreads);
        AtomicInteger messageIndex = new AtomicInteger(0);
        
        // 创建多个提交线程
        List<Thread> submitters = new ArrayList<>();
        for (int i = 0; i < submitterThreads; i++) {
            Thread submitter = new Thread(() -> {
                try {
                    startLatch.await(); // 等待同时开始
                    for (int j = 0; j < messagesPerThread; j++) {
                        int index = messageIndex.incrementAndGet();
                        TestMessage message = new TestMessage("test-" + index, "payload-" + index);
                        singleThreadWorker.submitTask(message);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    completeLatch.countDown();
                }
            });
            submitters.add(submitter);
            submitter.start();
        }
        
        // 开始提交
        startLatch.countDown();
        
        // 等待所有提交完成
        completeLatch.await();
        
        // 等待处理完成
        long startTime = System.nanoTime();
        while (singleThreadWorker.getProcessedCount() < totalMessages) {
            if (System.nanoTime() - startTime > TimeUnit.SECONDS.toNanos(30)) {
                throw new RuntimeException("SingleThreadTaskWorker 处理超时");
            }
            Thread.yield();
        }
        
        int processed = singleThreadWorker.getProcessedCount();
        blackhole.consume(processed);
        return processed;
    }

    /**
     * 多线程提交基准测试：SingleThreadDisruptorTaskWorker
     * 模拟真实场景：多个线程同时提交，单线程执行
     */
    @Benchmark
    public long benchmarkDisruptorTaskWorkerMultiSubmit(Blackhole blackhole) throws InterruptedException {
        if (disruptorWorker == null) {
            return 0;
        }
        
        ((TestDisruptorTaskWorker) disruptorWorker).reset();
        
        int totalMessages = submitterThreads * messagesPerThread;
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(submitterThreads);
        AtomicInteger messageIndex = new AtomicInteger(0);
        
        // 创建多个提交线程
        List<Thread> submitters = new ArrayList<>();
        for (int i = 0; i < submitterThreads; i++) {
            Thread submitter = new Thread(() -> {
                try {
                    startLatch.await(); // 等待同时开始
                    for (int j = 0; j < messagesPerThread; j++) {
                        int index = messageIndex.incrementAndGet();
                        TestMessage message = new TestMessage("test-" + index, "payload-" + index);
                        ((TestDisruptorTaskWorker) disruptorWorker).submitTask(message);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    completeLatch.countDown();
                }
            });
            submitters.add(submitter);
            submitter.start();
        }
        
        // 开始提交
        startLatch.countDown();
        
        // 等待所有提交完成
        completeLatch.await();
        
        // 等待处理完成
        long startTime = System.nanoTime();
        while (((TestDisruptorTaskWorker) disruptorWorker).getProcessedCount() < totalMessages) {
            if (System.nanoTime() - startTime > TimeUnit.SECONDS.toNanos(30)) {
                throw new RuntimeException("DisruptorTaskWorker 处理超时");
            }
            Thread.yield();
        }
        
        int processed = ((TestDisruptorTaskWorker) disruptorWorker).getProcessedCount();
        blackhole.consume(processed);
        return processed;
    }

    /**
     * 延迟测试：SingleThreadTaskWorker (单线程场景)
     */
    //@Benchmark  // 暂时注释掉，专注多线程测试
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.NANOSECONDS)
    public long benchmarkSingleThreadTaskWorkerLatency(Blackhole blackhole) throws InterruptedException {
        singleThreadWorker.reset();
        
        TestMessage testMessage = new TestMessage("latency-test", "latency-payload");
        long startTime = System.nanoTime();
        
        singleThreadWorker.submitTask(testMessage);
        
        // 等待处理完成
        while (singleThreadWorker.getProcessedCount() < 1) {
            Thread.yield();
        }
        
        long latency = System.nanoTime() - startTime;
        blackhole.consume(latency);
        return latency;
    }

    /**
     * 延迟测试：SingleThreadDisruptorTaskWorker (单线程场景)
     */
    //@Benchmark  // 暂时注释掉，专注多线程测试
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.NANOSECONDS)
    public long benchmarkDisruptorTaskWorkerLatency(Blackhole blackhole) throws InterruptedException {
        if (disruptorWorker == null) {
            return 0;
        }
        
        ((TestDisruptorTaskWorker) disruptorWorker).reset();
        
        TestMessage testMessage = new TestMessage("latency-test", "latency-payload");
        long startTime = System.nanoTime();
        
        ((TestDisruptorTaskWorker) disruptorWorker).submitTask(testMessage);
        
        // 等待处理完成
        while (((TestDisruptorTaskWorker) disruptorWorker).getProcessedCount() < 1) {
            Thread.yield();
        }
        
        long latency = System.nanoTime() - startTime;
        blackhole.consume(latency);
        return latency;
    }

    /**
     * 多线程提交延迟测试：SingleThreadTaskWorker
     * 测试在并发提交时，单次提交操作的平均延迟
     */
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.MICROSECONDS)
    public double benchmarkSingleThreadTaskWorkerSubmitLatency(Blackhole blackhole) throws InterruptedException {
        singleThreadWorker.reset();
        
        // 并发提交延迟测试
        int testCount = 100;
        CountDownLatch latch = new CountDownLatch(testCount);
        AtomicLong totalSubmitTime = new AtomicLong();
        
        for (int i = 0; i < testCount; i++) {
            new Thread(() -> {
                try {
                    TestMessage message = new TestMessage("latency-test", "payload");
                    long start = System.nanoTime();
                    singleThreadWorker.submitTask(message);
                    long submitTime = System.nanoTime() - start;
                    totalSubmitTime.addAndGet(submitTime);
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        latch.await();
        double avgSubmitTime = totalSubmitTime.get() / (double) testCount / 1000.0; // 转换为微秒
        blackhole.consume(avgSubmitTime);
        return avgSubmitTime;
    }

    /**
     * 多线程提交延迟测试：SingleThreadDisruptorTaskWorker
     * 测试在并发提交时，单次提交操作的平均延迟
     */
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.MICROSECONDS)
    public double benchmarkDisruptorTaskWorkerSubmitLatency(Blackhole blackhole) throws InterruptedException {
        if (disruptorWorker == null) {
            return 0;
        }
        
        ((TestDisruptorTaskWorker) disruptorWorker).reset();
        
        // 并发提交延迟测试
        int testCount = 100;
        CountDownLatch latch = new CountDownLatch(testCount);
        AtomicLong totalSubmitTime = new AtomicLong();
        
        for (int i = 0; i < testCount; i++) {
            new Thread(() -> {
                try {
                    TestMessage message = new TestMessage("latency-test", "payload");
                    long start = System.nanoTime();
                    ((TestDisruptorTaskWorker) disruptorWorker).submitTask(message);
                    long submitTime = System.nanoTime() - start;
                    totalSubmitTime.addAndGet(submitTime);
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        latch.await();
        double avgSubmitTime = totalSubmitTime.get() / (double) testCount / 1000.0; // 转换为微秒
        blackhole.consume(avgSubmitTime);
        return avgSubmitTime;
    }

    /**
     * 运行基准测试的主方法
     */
    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(WorkerJMHBenchmark2.class.getSimpleName())
                .shouldFailOnError(true)
                .shouldDoGC(true)
                .jvmArgs("-Xmx4g", "-XX:+UseZGC")
                .build();

        new Runner(opt).run();
    }
} 