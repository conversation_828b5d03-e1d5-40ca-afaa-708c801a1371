package com.lc.billion.icefire.game.biz.service.impl.expedition;

import com.alibaba.fastjson.JSONObject;
import com.google.cloud.Tuple;
import com.google.common.collect.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.*;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleExpeditionDao;
import com.lc.billion.icefire.game.biz.manager.RoleHeroManager;
import com.lc.billion.icefire.game.biz.model.alipay.AlipayReportType;
import com.lc.billion.icefire.game.biz.model.currency.Currency;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionBtlStatus;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionErrorCode;
import com.lc.billion.icefire.game.biz.model.expedition.ExpeditionLevelType;
import com.lc.billion.icefire.game.biz.model.expedition.RoleExpedition;
import com.lc.billion.icefire.game.biz.model.hero.HeroBattleInfo;
import com.lc.billion.icefire.game.biz.model.item.ItemUtils;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.people.PeopleBornConditionType;
import com.lc.billion.icefire.game.biz.model.prop.Prop;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.alipay.AlipayReportServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.drop.DropServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroOutput;
import com.lc.billion.icefire.game.biz.service.impl.hero.HeroServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.rpc.impl.battle.BattleServiceImpl;
import com.lc.billion.icefire.game.biz.util.RandSeedUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.lc.billion.icefire.protocol.constant.UnlockModule;
import com.lc.billion.icefire.protocol.structure.PsExpeditionBtlVerify;
import com.lc.billion.icefire.protocol.structure.PsHeroEquip;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * @ClassName ExpeditionServiceImpl
 * @Description 需要将ts服务的主线关卡移到java中  进行中
 * <AUTHOR>
 * @Date 2023/12/7 15:48
 * @Version 1.0
 */
@Service
public class ExpeditionServiceImpl {
    private final static Logger logger = LoggerFactory.getLogger(ExpeditionServiceImpl.class);

    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private HeroServiceImpl heroService;
    @Autowired
    private RoleExpeditionDao expeditionDao;
    @Autowired
    private RoleHeroManager roleHeroManager;
    @Autowired
    private BattleServiceImpl battleService;
    @Autowired
    private AlipayReportServiceImpl alipayReportService;
    @Autowired
    private ConfigServiceImpl configService;

    private final int LINEUP_SIZE = 5;
    /**
     * ab测试
     */
    private final int LINEUP_SIZE_AB_TEST = 6;

    private final int LINEUP_PLACEHOLDER = 0;

//    private final int TIME_OUT = 6;

    public void onEnterWorld(Role role) {
        // 给用户发送当前的信息
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            expedition = expeditionDao.create(role);
            // 初始化随机种子时处理一次，避免数据太大超过客户端精确计算范围
            expedition.setSeed(RandSeedUtil.rand(role.getRoleId()).seed);
            expeditionDao.save(expedition);
        }
        // 获取数据
        LevelConfig levelConfig = configService.getConfig(LevelConfig.class);
        var nextLevel = levelConfig.getNextLevel(expedition.getLevel());
        role.send(wrapperGcExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup(), expedition.getGroupId(),
                expedition.getRewardGroupId()));

        // 计算挂机奖励发给客户端
        if (nextLevel != levelConfig.getInitLevel()) {
            calcHangup(role, true, true);
        }

        sendInnerFinishedIds(role);
        // 重新登录时设置为初始化状态
        expedition.status.set(ExpeditionBtlStatus.INIT);
    }

    public long findSeed(Role role) {
        var expedition = expeditionDao.findById(role.getRoleId());
        return expedition != null ? expedition.getSeed() : 0L;
    }

    private GcExpeditionInfo wrapperGcExpeditionInfo(int level, long seed, List<Integer> heroIds, int groupId, int rewardGroupId) {
        return new GcExpeditionInfo()
                .setLevel(level)
                .setSeed(seed)
                .setLineup(heroIds)
                .setGroupId(groupId)
                .setRewardGroupId(rewardGroupId);
    }

    public boolean isPassed(Role role, int level) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return false;
        }
        return expedition.getLevel() >= level;
    }

    public void updateLevel(Role role, int level) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            expedition = expeditionDao.create(role);
        }
        if (expedition.getLevel() >= level) {
            ErrorLogUtil.errorLog("ExpeditionService update targetLevel error", "new", level, "old", expedition.getLevel());
            return;
        }
        expedition.setLevel(level);
        expeditionDao.save(expedition);

        var netLevel = configService.getConfig(LevelConfig.class).getNextLevel(expedition.getLevel());
        // 获取数据
        role.send(wrapperGcExpeditionInfo(netLevel, expedition.getSeed(), Lists.newArrayList(), expedition.getGroupId(), expedition.getRewardGroupId()));

        onLevelUpdate(role, level);
    }

    /**
     * 关卡变化后需要处理的事项
     *
     * @param role
     * @param level
     */
    private void onLevelUpdate(Role role, int level) {
        var roleId = role.getRoleId();
        // 发奖励
        var levelMeta = configService.getConfig(LevelConfig.class).getLevelMetaByLevel(level);
        if (levelMeta == null) {
            ErrorLogUtil.errorLog("chapter targetLevel is invalid", "roleId", roleId, "level", level);
            return;
        }

        // 发送奖励
        DropGroupConfig.DropGroupMeta dropMeta = configService.getConfig(DropGroupConfig.class).get(String.valueOf(levelMeta.winRewardId()));
        List<SimpleItem> drops = Lists.newArrayList();
        if (dropMeta != null) {
            drops = DropServiceImpl.dropsMerge(srvDpd.getDropService().drop(dropMeta));
            srvDpd.getItemService().give(role, drops, LogReasons.ItemLogReason.PVE_BATTER_WIN_REWARD);
        }

        // 发送奖励消息
        GcExpeditionReward gcMsg = new GcExpeditionReward();
        gcMsg.setLevel(level);
        gcMsg.setItems(ItemUtils.toPsSimpleItem(drops));
        role.send(gcMsg);
        // 功能解锁更新
        srvDpd.getUnlockService().onExpeditionLevelChanged(role, level);

        srvDpd.getMissionService().onMissionFinish(role, MissionType.CHAPTER_LEVEL_UPDATE, level);

        srvDpd.getLiBaoService().liBaoUpdateOnCheck(role, "expeditionPass", LiBaoFunctionType.EXPEDITION_LEVEL, level);
    }

    /**
     * 挂机奖励涉及多个时间
     * 挂机总持续时间：用来标记计算的总时长
     * 计算时间：用来标记当前一次计算的时间
     * 道具和经验时间：每个组对应着不同的时间，主要是数值问题
     * 装备时间：每个组时间不同，可以结余时间
     *
     * @param role
     * @param readOnly
     * @param needNotify
     */
    public void calcHangup(Role role, boolean readOnly, boolean needNotify) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return;
        }
        long now = TimeUtil.getNow();
        // 第一关通过，需要初始化挂机奖励
        if (!srvDpd.getUnlockService().isUnlock(role, UnlockModule.Hangup)) {
            return;
        } else {
            // 刚解锁，需要初始化时间
            if (expedition.getDuration() == 0 && expedition.getCalcHangupTime() == 0) {
                expedition.setCalcHangupTime(now);
                expeditionDao.save(expedition);
            }
        }
        LevelConfig levelConfig = configService.getConfig(LevelConfig.class);
//        var initLevel = levelConfig.getInitLevel();
//        if (initLevel > expedition.getLevel()) {
//            return;
//        } else if (initLevel == levelConfig.getNextLevel(expedition.getLevel())) {
//            expedition.setCalcHangupTime(now);
//            expedition.setHangupMap(new HashMap<>());
//            expeditionDao.save(expedition);
//        }
        var meta = levelConfig.getLevelMetaByLevel(expedition.getLevel());
        if (meta == null) {
            return;
        }
        // 重新计算
        var delta = now - expedition.getCalcHangupTime();
        long duration = expedition.getDuration();
        long MAX_HANGUP_TIME = configService.getConfig(SettingConfig.class).getExpeditionHangupMaxSeconds() * 1000L - duration;
        if (delta > MAX_HANGUP_TIME) {
            delta = MAX_HANGUP_TIME;
        }
        // 两步，1 计算道具和经验  2 计算装备
        // 1 计算
        var hangupMap = expedition.getHangupMap();
        var hangupItemTimeList = expedition.getHangupItemTimeList();
        int index = 0;
        for (var rewardType : meta.getHangupMap().keySet()) {
            var count = hangupMap.computeIfAbsent(rewardType, k -> 0L);
            // 1 计算多少分钟 和剩余时间
            long leftTime = 0L;
            if (index == hangupItemTimeList.size()) {
                hangupItemTimeList.add(leftTime);
            } else {
                leftTime = hangupItemTimeList.get(index);
            }
            // 计算道具剩余时间
            double hours = (double) (delta + leftTime) / TimeUtil.HOUR_MILLIS;
            double speed = meta.getHangupMap().get(rewardType);
            // 判断是否有加成，只有货币存在加成
            if (Currency.findById(MetaUtils.tryParseInt(rewardType)) != null) {
                speed *= 1 + role.getNumberProps().getDouble(Prop.EXPEDITION_HANGUP_ADD_RATIO);
            }
            double realCount = hours * speed;
            long addCount = (long) Math.floor(realCount);
            hangupMap.put(rewardType, addCount + count);
            // 计算道具剩余时间
            leftTime = (long) (delta + leftTime - addCount / speed * TimeUtil.HOUR_MILLIS);
            if (leftTime < 0) {
                leftTime = 0L;
            }
            hangupItemTimeList.set(index, leftTime);
            index++;
        }

        // 2 计算装备
        var equipDropRemainList = expedition.getHangupEquipDropTimeList();
        int equipCount = 0;
        for (int i = 0; i < meta.getHangupEquipList().size(); i++) {
            long equipDropRemain = 0L;
            if (i >= equipDropRemainList.size()) {
                equipDropRemainList.add(equipDropRemain);
            } else {
                equipDropRemain = equipDropRemainList.get(i);
            }
            equipDropRemain += delta;
            var equipDropUnit = meta.getHangupEquipList().get(i);
            equipCount = (int) Math.floor((double) equipDropRemain / (equipDropUnit.time * TimeUtil.SECONDS_MILLIS));
            equipDropRemain -= equipCount * equipDropUnit.time * TimeUtil.SECONDS_MILLIS;
            equipDropRemainList.set(i, equipDropRemain);
            // 获取掉落组信息
            for (int j = 0; j < equipCount; j++) {
                var drops = srvDpd.getDropService().drop(equipDropUnit.dropId);
                for (var drop : drops) {
                    hangupMap.merge(drop.getMetaId(), 1L, Long::sum);
                }
            }
        }

        expedition.setCalcHangupTime(now);
        expedition.setHangupMap(hangupMap);
        expedition.setDuration(duration + delta);

        Map<String, Long> retHangupMap = new HashMap<>();
        hangupMap.forEach((k, v) -> {
            if (v > 0) {
                retHangupMap.put(k, v);
            }
        });

        if (!readOnly) {
            // 需要获取
            getHangupReward(role);
        }
        if (needNotify) {
            role.send(wrapperGcExpeditionHangup(readOnly, expedition.getCalcHangupTime() - expedition.getDuration(), retHangupMap));
        }

        expeditionDao.save(expedition);
    }

    private void getHangupReward(Role role) {
        if (!srvDpd.getUnlockService().isUnlock(role, UnlockModule.Hangup)) {
            return;
        }
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null || expedition.getHangupMap().isEmpty()) {
            return;
        }
        List<Tuple<String, Long>> rewardList = new ArrayList<>();
        // 发送奖励
        var hangupMap = expedition.getHangupMap();
        for (var rewardType : hangupMap.keySet()) {
            var currencyType = Currency.findById(Integer.parseInt(rewardType));
            Long giveCount = hangupMap.get(rewardType);
            if (null == giveCount || giveCount <= 0) {
                continue;
            }
            if (currencyType != null) {
                srvDpd.getRoleCurrencyManager().add(role, currencyType, giveCount, LogReasons.MoneyLogReason.PVE_EXPEDITION_HANGUP_REWARD, "探險挂机奖励");
            } else {
                // 道具这里long->int可能存在问题 TODO
                srvDpd.getItemService().give(role, new SimpleItem(rewardType, Math.toIntExact(giveCount)), LogReasons.ItemLogReason.PVE_EXPEDITION_HANGUP_REWARD);
            }
            rewardList.add(Tuple.of(rewardType, giveCount));
        }
        hangupMap.clear();
        var duration = expedition.getDuration();
        expedition.setHangupMap(hangupMap);
        expedition.setDuration(0);
        expedition.setCalcHangupTime(TimeUtil.getNow());
        expedition.setPushClient(false);
        expeditionDao.save(expedition);

        srvDpd.getBiLogUtil().expeditionGetHangup(role, duration, rewardList);

        srvDpd.getMissionService().onMissionFinish(role,MissionType.RECEIVE_HUNT_REWARD_NUM);
    }

    private GcExpeditionBtlResult wrapperGcExpeditionBtlResult(ExpeditionErrorCode errorCode, boolean isWin, int nextLevel, List<SimpleItem> drops) {
        return new GcExpeditionBtlResult()
                .setErrorCode(errorCode.getId())
                .setIsWin(isWin)
                .setNextLevel(nextLevel)
                .setItems(ItemUtils.toPsSimpleItem(drops));
    }

    /**
     * 阵容检查
     * 1. 数量一直， 2. 英雄是否不重复，切拥有
     *
     * @param lineup
     * @return true: 合规
     */
    private boolean checkLineup(Role role, List<Integer> lineup) {
        if (lineup == null || (lineup.size() != LINEUP_SIZE && lineup.size() != LINEUP_SIZE_AB_TEST)) {
            return false;
        }

        int placeholderCount = Collections.frequency(lineup, LINEUP_PLACEHOLDER);
        if (placeholderCount >= lineup.size()) {
            return false;
        }

        for (var heroId : lineup) {
            if (heroId == LINEUP_PLACEHOLDER) {
                continue;
            }
            if (!heroService.hasHero(role, heroId.toString())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查随机种子
     *
     * @param role
     * @param expedition
     * @param seed
     * @param seedIndex
     * @return
     */
    private boolean checkRandSeed(Role role, RoleExpedition expedition, long seed, int seedIndex) {
        if (seedIndex <= 0) {
            return false;
        }
        for (int i = 0; i < seedIndex; i++) {
            flushRandSeed(expedition);
        }
        expeditionDao.save(expedition);
        return expedition.getSeed() == seed;
    }

    /**
     * 检查随机种子
     *
     * @param role
     * @param seed
     * @param seedIndex
     * @return
     */
    public boolean checkRandSeed(Role role, long seed, int seedIndex) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            ErrorLogUtil.errorLog("ExpeditionServiceImpl checkRandSeed: 未找到关卡", "roleId", role.getRoleId());
            return false;
        }
        return checkRandSeed(role, expedition, seed, seedIndex);
    }

    public void flushRandSeed(Role role) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            ErrorLogUtil.errorLog("ExpeditionServiceImpl flushRandSeed: 未找到关卡", "roleId", role.getRoleId());
            return;
        }
        flushRandSeed(expedition);
    }

    private void flushRandSeed(RoleExpedition expedition) {
        var rand = RandSeedUtil.rand(expedition.getSeed());
        expedition.setSeed(rand.seed);
    }


    public boolean btlStart(Role role, int targetLevel, PsExpeditionBtlVerify data) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            role.send(wrapperGcExpeditionBtlResult(
                    ExpeditionErrorCode.EXPEDITION_NOT_FOUND,
                    false,
                    targetLevel,
                    Lists.newArrayList()
            ));
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 未找到关卡", "roleId", role.getRoleId(), "level", targetLevel);
            return false;
        }
        if (expedition.status.get() != ExpeditionBtlStatus.INIT && !expedition.changeUpdate(ExpeditionBtlStatus.END, ExpeditionBtlStatus.INIT)) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 状态异常", "roleId", role.getRoleId(), "status", expedition.status.get());
            role.send(wrapperGcExpeditionBtlResult(
                    ExpeditionErrorCode.LAST_BATTLE_NOT_FINISHED,

                    false,
                    targetLevel,
                    Lists.newArrayList()
            ));
            expedition.status.set(ExpeditionBtlStatus.INIT);
            return false;
        }
        LevelConfig levelConfig = configService.getConfig(LevelConfig.class);
        var nextLevel = levelConfig.getNextLevel(expedition.getLevel());
        // 1. 先判断关卡是否正确
        if (nextLevel != targetLevel) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 关卡不一致", "roleId", role.getRoleId(), "clientLevel", targetLevel, "serverLevel", nextLevel);
            role.send(wrapperGcExpeditionBtlResult(
                    ExpeditionErrorCode.PARAMETER_ERROR,
                    false,
                    nextLevel,
                    Lists.newArrayList()
            ));
            expedition.status.set(ExpeditionBtlStatus.END);
            return false;
        }
        var levelType = levelConfig.getLevelType(targetLevel);
        // 剧情关卡 直接跳过，但需要修改一次随机种子
        if (levelType == ExpeditionLevelType.STORY) {
            // 刷新种子
            flushRandSeed(expedition);
            onBattleEnd(role, expedition.getNewRecordId(), targetLevel, true, true, false);
            expeditionDao.save(expedition);
            return true;
        } else if (data == null) {
            GcExpeditionBtlResult retMsg = new GcExpeditionBtlResult();
            retMsg.setErrorCode(ExpeditionErrorCode.PARAMETER_ERROR.getId());
            retMsg.setIsWin(false);
            role.send(retMsg);
            return false;
        }

        // 判断是否可以跳过 低于300关 并且为 5 10 的关卡可以跳过
        if (targetLevel <= 300 && targetLevel % 5 != 0) {
            // 修改随机种子
            if (!checkRandSeed(role, expedition, data.getData().getRandseed(), data.getSeedIndex())) {
                role.send(wrapperGcExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup(),
                        expedition.getGroupId(), expedition.getRewardGroupId()));
            }
            onBattleEnd(role, expedition.getNewRecordId(), targetLevel, true, false, false);
            expeditionDao.save(expedition);
            return true;
        }

        // 2. 判断阵容
        var lineup = data.getLineup();
        if (!checkLineup(role, lineup)) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 阵容不合规", "client", data.getLineup());
            role.send(wrapperGcExpeditionBtlResult(
                    ExpeditionErrorCode.LINEUP_ERROR,
                    false,
                    nextLevel,
                    Lists.newArrayList()
            ));
            expedition.status.set(ExpeditionBtlStatus.END);
            return false;
        }
        long oldSeed = expedition.getSeed();
        // 3. 判断随机种子
        if (!checkRandSeed(role, expedition, data.getData().getRandseed(), data.getSeedIndex())) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 随机种子不一致", "roleId", role.getRoleId(),
                    "client", data.getData().getRandseed(), "server", expedition.getSeed(), "old", oldSeed,
                    "index", data.getSeedIndex());
            role.send(wrapperGcExpeditionBtlResult(
                    ExpeditionErrorCode.RAND_SEED_NOT_SAME,
                    false,
                    nextLevel,
                    Lists.newArrayList()
            ));
            // 种子不一样给客户端返回对的
            role.send(wrapperGcExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup(),
                    expedition.getGroupId(), expedition.getRewardGroupId()));
            expedition.status.set(ExpeditionBtlStatus.END);
            return false;
        }
        // 4. 参数统计
        int recordId = expedition.getNewRecordId();
        Map<String, HeroBattleInfo> heroMap = new HashMap<>();
        Map<String, PsHeroEquip> equipMap = new HashMap<>();
        for (var heroId : lineup) {
            if (heroId == LINEUP_PLACEHOLDER) {
                continue;
            }
            var hero = heroService.getHero(role, heroId.toString());

            if (hero != null) {
                double lordSkillAddition = roleHeroManager.lordSkillAddition(role, hero);
                heroMap.put(String.valueOf(heroId), HeroOutput.toBattleInfo(hero, lordSkillAddition));
                var equips = heroService.getHeroEquips(hero);
                for (var equip : equips) {
                    var psEquip = new PsHeroEquip(
                            equip.getId().toString(),
                            equip.getMetaId().toString(),
                            equip.getHeroId().toString(),
                            equip.getExps(),
                            equip.getLevel());
                    psEquip.setCfgId(equip.getMetaId());
                    psEquip.setForge(equip.getForge());
                    equipMap.put(equip.getId().toString(), psEquip);
                }
            }
        }

        // 5. 请求战斗
        expedition.setLineup(lineup);

        if (!toTsBtlStart(role, expedition, recordId, targetLevel, heroMap, data, equipMap)) {
            // 战斗校验异常
            role.send(wrapperGcExpeditionBtlResult(
                    ExpeditionErrorCode.TIME_OUT,
                    false,
                    nextLevel,
                    Lists.newArrayList()
            ));
            expedition.status.set(ExpeditionBtlStatus.END);
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 战斗校验异常", "roleId", role.getRoleId());
        }
        expeditionDao.save(expedition);
        return true;
        // 6. 设置超时
//        scheduleService.asyncSchedule(new AsyncOperation() {
//            @Override
//            public boolean run() {
//                if (expedition.changeUpdate(ExpeditionBtlStatus.WAITING, ExpeditionBtlStatus.TIME_OUT)) {
//                    logger.info("CgExpeditionBtlVerifyHandler: ExpeditionBtlStatus.WAITING -> ExpeditionBtlStatus.TIME_OUT");
//                    // 超时处理
//                    role.send(wrapperGcExpeditionBtlResult(
//                            ExpeditionErrorCode.TIME_OUT,
//                            false,
//                            nextLevel,
//                            Lists.newArrayList()
//                    ));
//                }
//                return true;
//            }
//
//            @Override
//            public void finish() {
//                if (expedition.changeUpdate(ExpeditionBtlStatus.TIME_OUT, ExpeditionBtlStatus.END)) {
//                    expeditionDao.save(expedition);
//                }
//            }
//
//            @Override
//            public void exec() {
//                AsyncOperation.super.exec();
//            }
//        }, TIME_OUT * TimeUtil.SECONDS_MILLIS);
    }

    private boolean toTsBtlStart(Role role, RoleExpedition expedition, int recordId, int level, Map<String, HeroBattleInfo> heroMap, PsExpeditionBtlVerify data, Map<String, PsHeroEquip> equipMap) {
        try {
            if (expedition.changeUpdate(ExpeditionBtlStatus.INIT, ExpeditionBtlStatus.WAITING)) {

                JSONObject obj = new JSONObject();
                obj.put("roleId", role.getRoleId());
                obj.put("recordId", recordId);
                obj.put("level", level);
                obj.put("heroes", heroMap);
                obj.put("verifyData", data);
                obj.put("equipMap", equipMap);
                battleService.expeditionVerify(obj.toJSONString(), resp -> {
                    if (resp != null) {
                        logger.info("CgExpeditionBtlVerifyHandler: expeditionVerify success: {}", resp);
                        if (!resp.isWin()) {
                            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: expeditionVerify error", "description", obj.toJSONString());
                        }
                        onBattleEnd(role, resp.getRecordId(), resp.getLevel(), resp.isWin(), false, resp.isAbTest());
                    } else {
                        // 战斗校验异常
                        role.send(wrapperGcExpeditionBtlResult(
                                ExpeditionErrorCode.TIME_OUT,
                                false,
                                level,
                                Lists.newArrayList()
                        ));
                        expedition.status.set(ExpeditionBtlStatus.END);
                        ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: 战斗校验异常", "roleId", role.getRoleId());
                    }
                });
                //
                return true;

            }
            return false;
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("CgExpeditionBtlVerifyHandler: expeditionVerify error", e);
            }
            return false;
//            role.send(wrapperGcExpeditionBtlResult(
//                    ExpeditionErrorCode.TIME_OUT,
//                    false,
//                    level,
//                    Lists.newArrayList()
//            ));
//            expedition.status.set(ExpeditionBtlStatus.END);
//            throw new RuntimeException(e);
        }
    }

    public void onBattleEnd(Role role, int recordId, int targetLevel, boolean isWin, boolean isSkip, boolean isAbTest) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: onEnd 未找到数据", "roleId", role.getRoleId(), "recordId", recordId);
            return;
        }
        // 先修改状态
        expedition.changeUpdate(ExpeditionBtlStatus.WAITING, isWin ? ExpeditionBtlStatus.WIN : ExpeditionBtlStatus.LOSE);
//        if (!expedition.changeUpdate(ExpeditionBtlStatus.WAITING, isWin ? ExpeditionBtlStatus.WIN : ExpeditionBtlStatus.LOSE)) {
//            logger.error("CgExpeditionBtlVerifyHandler: onEnd 修改状态失败 {} {}", role.getRoleId(), expedition.status.get());
//            return;
//        }
        if (expedition.getRecordId() != recordId) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: onEnd recordId 不一致", "roleId", role.getRoleId(), "recordId", recordId);
            return;
        }
        LevelConfig levelConfig = configService.getConfig(LevelConfig.class);
        var nextLevel = levelConfig.getNextLevel(expedition.getLevel());
        if (nextLevel != targetLevel) {
            ErrorLogUtil.errorLog("CgExpeditionBtlVerifyHandler: onEnd targetLevel 不一致", "roleId", role.getRoleId(), "targetLevel", targetLevel, "nextLevel", nextLevel);
            return;
        }
        var roleId = role.getRoleId();
        // 战斗胜利后的处理

        // 发送奖励
        List<SimpleItem> drops = Lists.newArrayList();
        LevelConfig.LevelMeta levelMeta = null;
        if (isWin) {
            levelMeta = levelConfig.getLevelMetaByLevel(targetLevel);
            if (levelMeta != null) {
                DropGroupConfig.DropGroupMeta dropMeta = configService.getConfig(DropGroupConfig.class).get(String.valueOf(levelMeta.winRewardId()));
                if (dropMeta != null) {
                    drops = DropServiceImpl.dropsMerge(srvDpd.getDropService().drop(dropMeta));
                    srvDpd.getItemService().give(role, drops, LogReasons.ItemLogReason.PVE_BATTER_WIN_REWARD);
                }
            }
            // 修改关卡数据
            expedition.setLevel(targetLevel);
            nextLevel = levelConfig.getNextLevel(targetLevel);

            // 修改任务
            srvDpd.getMissionService().onMissionFinish(role, MissionType.CHAPTER_LEVEL_UPDATE, targetLevel);

            // 功能解锁
            srvDpd.getUnlockService().onExpeditionLevelChanged(role, targetLevel);

            //支付宝讨伐(主线)胜利时触发
            alipayReportService.onAlipayReportProgress(role, AlipayReportType.CHAPTER_LEVEL_UPDATE);
        }

        role.send(wrapperGcExpeditionBtlResult(ExpeditionErrorCode.NORMAL, isWin, nextLevel, drops));
        calcHangup(role, true, false);
        expedition.status.set(ExpeditionBtlStatus.END);
        expeditionDao.save(expedition);

        if (!isSkip) {
            var lineup = expedition.getLineup();
            Map<Integer, Object> heroMap = new HashMap<>();
            long power = 0;
            for (var heroId : lineup) {
                if (heroId == LINEUP_PLACEHOLDER) {
                    continue;
                }
                var hero = heroService.getHero(role, heroId.toString());
                power += hero.getPower();
                Map<String, Object> map = new HashMap<>();
                map.put("heroId", hero.getId());
                map.put("power", hero.getPower());
                map.put("star", hero.getStar());
                map.put("exclusiveLevel", hero.getExclusiveLevel());
                map.put("level", hero.getLevel());
                map.put("equipIds", hero.getEquipIds());
                heroMap.put(heroId, map);
            }
            int abType = isAbTest ? 1 : 0;
            // TODO 处理老用户，需要产品提供时间节点
            srvDpd.getBiLogUtil().expeditionBattleEnd(role, isWin, targetLevel, power, lineup, drops, heroMap, abType);
        }
        // 关卡变化打点
        if (levelMeta != null) {
            srvDpd.getBiLogUtil().roleProfileExpeditionLevelChange(role, levelMeta.getName());
        }

        srvDpd.getMissionService().onMissionFinish(role,MissionType.ENGAGE_BATTLE_COUNT);
    }

    private GcExpeditionHangup wrapperGcExpeditionHangup(boolean readOnly, Long startTime, Map<String, Long> hangupMap) {
        GcExpeditionHangup msg = new GcExpeditionHangup();
        msg.setReadOnly(readOnly);
        msg.setStartTime(startTime);
        msg.setHangupMap(hangupMap);
        return msg;
    }

    // 获得章节奖励
    public void groupReward(Role role, int groupId) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            ErrorLogUtil.errorLog("expedition is not find", "roleId", role.getRoleId());
            return;
        }

        // 已经领过了
        var rewardGroupId = expedition.getRewardGroupId();
        if (rewardGroupId >= groupId) {
            ErrorLogUtil.errorLog("expedition is rewarded", "roleId", role.getRoleId(), "groupId", groupId);
            return;
        }

        // 是否可以领取了
        var levelMax = configService.getConfig(LevelConfig.class).getGroupMax(groupId);
        if (levelMax <= 0 || levelMax > expedition.getLevel()) {
            ErrorLogUtil.errorLog("expedition isn't enough", "roleId", role.getRoleId(), "groupId", groupId, "level", expedition.getLevel(), "levelMax", levelMax);
            return;
        }

        // 查表
        var levelGroupMeta = configService.getConfig(LevelGroupConfig.class).findById(String.valueOf(groupId));
        if (null == levelGroupMeta) {
            ErrorLogUtil.errorLog("expedition meta isn't find", "roleId", role.getRoleId(), "groupId", groupId);
            return;
        }

        // 发送奖励
        List<SimpleItem> drops = Lists.newArrayList();
        var dropGroupConfig = configService.getConfig(DropGroupConfig.class);
        var dropMeta = dropGroupConfig.get(levelGroupMeta.getRewardId());
        if (dropMeta != null) {
            drops = DropServiceImpl.dropsMerge(srvDpd.getDropService().drop(dropMeta));
            srvDpd.getItemService().give(role, drops, LogReasons.ItemLogReason.PVE_EXPEDITION_GROUP_HANGUP_REWARD);
        }

        // 添加领取记录
        expedition.setRewardGroupId(groupId);

        GcExpeditionGroupReward msg = new GcExpeditionGroupReward();
        msg.setGroupId(expedition.getRewardGroupId());
        for (var entry : drops) {
            msg.putToRewardMap(entry.getMetaId(), entry.getCount());
        }
        role.send(msg);
        expeditionDao.save(expedition);
    }

    // 进入下一章
    public void groupNext(Role role, int groupId) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return;
        }

        // 章节是否存在
        var nextMeta = configService.getConfig(LevelGroupConfig.class).findById(String.valueOf(groupId));
        if (null == nextMeta) {
            ErrorLogUtil.errorLog("group is not find", "groupId", groupId);
            return;
        }

        // 该章节的关卡是否已经通关
        var maxLevel = configService.getConfig(LevelConfig.class).getGroupMax(groupId - 1);
        if (maxLevel > expedition.getLevel()) {
            ErrorLogUtil.errorLog("level is not enough", "groupId", groupId, "level", expedition.getLevel(), "needLevel", maxLevel);
            return;
        }

        // 当前章节
        var curGroupId = expedition.getGroupId();
        // 设置章节
        expedition.setGroupId(groupId);
        logger.info("level set success groupId:{} level:{} old groupId:{}.", groupId, expedition.getLevel(), curGroupId);

        var msg = new GcExpeditionGroupNext();
        msg.setGroupId(groupId);
        role.send(msg);
    }

    public GcExpeditionInnerReward wrapperGcExpeditionInnerReward(PsErrorCode code, List<SimpleItem> items) {
        GcExpeditionInnerReward msg = new GcExpeditionInnerReward();
        msg.setCode(code);
        msg.setItems(ItemUtils.toPsSimpleItem(items));
        return msg;
    }

    public void getInnerReward(Role role, List<Integer> levelIds) {
        List<SimpleItem> items = Lists.newArrayList();
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            role.send(wrapperGcExpeditionInnerReward(PsErrorCode.EXPEDITION_INNER_ERROR, items));
            return;
        }
        var innerPVEConfig = configService.getConfig(InnerPVEConfig.class);
        // 判断当前是否已经被领取过了
        // 判断是否达到了当前的进度
        for (var levelId : levelIds) {
            var config = innerPVEConfig.getMeta(levelId);
            if (config == null) {
                role.send(wrapperGcExpeditionInnerReward(PsErrorCode.EXPEDITION_INNER_ERROR, items));
                return;
            }
            if (expedition.getLevel() < config.getExpeditionLevelId()) {
                role.send(wrapperGcExpeditionInnerReward(PsErrorCode.EXPEDITION_INNER_REWARD_UNCLIMABLE, items));
                return;
            }
            if (expedition.getInnerReceivedIds().contains(levelId)) {
                role.send(wrapperGcExpeditionInnerReward(PsErrorCode.EXPEDITION_INNER_REWARD_RECEIVED, items));
                return;
            }
        }

        // 到了这一步肯定都可以领取了
        for (var levelId : levelIds) {
            var dropGroupId = innerPVEConfig.getDropGroupId(levelId);
            if (!dropGroupId.isEmpty()) {
                items.addAll(DropServiceImpl.dropsMerge(srvDpd.getDropService().drop(dropGroupId)));
            }
        }
        // 记录
        expedition.getInnerReceivedIds().addAll(levelIds);
        expeditionDao.save(expedition);
        // 发送通知
        role.send(wrapperGcExpeditionInnerReward(PsErrorCode.SUCCESS, items));
        // 变化更新通知
        sendInnerFinishedIds(role);
        // 触发任务变更
        srvDpd.getMissionService().onMissionFinish(role, MissionType.REVENDICATION_INNER_REWARD, role);
        // 触发收复地块的 来人逻辑
        srvDpd.getPeopleServiceImpl().onTriggerBorn(role, PeopleBornConditionType.REVENDICATION_INNER_REWARD, levelIds);
        srvDpd.getUnlockService().onInnerLevelChanged(role, getInnerLevel(role));
    }

    public void sendInnerFinishedIds(Role role) {
        var expedition = expeditionDao.findById(role.getRoleId());
        GcExpeditionInnerInfo msg = new GcExpeditionInnerInfo();
        msg.setFinishedLevelIds(expedition == null ? List.of() : expedition.getInnerReceivedIds());
        role.send(msg);
    }

    public void sendExpeditionInfo(Role role) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return;
        }
        // 发送关卡信息
        var nextLevel = configService.getConfig(LevelConfig.class).getNextLevel(expedition.getLevel());
        role.send(wrapperGcExpeditionInfo(nextLevel, expedition.getSeed(), expedition.getLineup(), expedition.getGroupId(),
                expedition.getRewardGroupId()));
    }

    public boolean isReachLevel(Role role, int level) {
        if (level <= 0) {
            return true;
        }
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return false;
        }
        return level <= expedition.getLevel();
    }

    public void lowTick(Role role, long now) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null || expedition.isPushClient()) {
            return;
        }

        SettingConfig settingConfig = configService.getConfig(SettingConfig.class);
        long MAX_HANGUP_TIME = settingConfig.getExpeditionHangupMaxSeconds() * TimeUtil.SECONDS_MILLIS;
        if (expedition.getDuration() + now - expedition.getCalcHangupTime() >= MAX_HANGUP_TIME) {
            srvDpd.getPushHelper().growthBattleReward(role);
            expedition.setPushClient(true);
            expeditionDao.save(expedition);
        }
    }

    public int getExpeditionLevel(Role role) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return 0;
        }
        return expedition.getLevel();
    }

    public void gmSetHangupTime(Role role, Long aLong) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return;
        }
        if (aLong == null) {
            aLong = configService.getConfig(SettingConfig.class).getExpeditionHangupMaxSeconds() * 1000L;
        }
        expedition.setCalcHangupTime(TimeUtil.getNow() - aLong);
        expeditionDao.save(expedition);
        calcHangup(role, true, true);
    }

    public int getInnerLevel(Role role) {
        var expedition = expeditionDao.findById(role.getRoleId());
        if (expedition == null) {
            return 0;
        }
        return expedition.getInnerReceivedIds().stream().max(Integer::compareTo).orElse(0);
    }

}
