package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.MetaUtils;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName InnerPVEConfig
 * @Description
 * <AUTHOR>
 * @Date 2024/4/2 16:11
 * @Version 1.0
 */
@Config(name = "InnerPVE", metaClass = InnerPVEConfig.InnerPVEMeta.class)
public class InnerPVEConfig {

    private final Map<Integer, InnerPVEMeta> metaMap = new HashMap<>();

    public void init(List<InnerPVEMeta> metaList) {
        metaList.forEach(meta -> {
            metaMap.put(meta.getLevelId(), meta);
        });
    }

    public String getDropGroupId(int levelId) {
        var meta = metaMap.get(levelId);
        if (meta == null) {
            return "";
        }
        return meta.dropGroupId;
    }

    public InnerPVEMeta getMeta(int levelId) {
        return metaMap.get(id);
    }

    @Getter
    @Setter
    public static class InnerPVEMeta extends AbstractMeta {

        private int levelId;

        private int expeditionLevelId;

        private String dropGroupId;

        private int winEventType;

        private String winEventParams;

        @Override
        public void init(JsonNode json) {
            expeditionLevelId = winEventType == 1 ? Objects.requireNonNull(MetaUtils.parseInts(winEventParams, AbstractMeta.META_SEPARATOR_3))[0] : 0;
        }

    }
}
