package com.lc.billion.icefire.game.biz.exec;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.BizException;
import com.lc.billion.icefire.game.biz.model.world.WorldWorker;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.slow.SlowLogUtil;
import com.lc.billion.icefire.game.msg.GlobalMessageExecutor;
import com.simfun.sgf.common.tuple.TwoTuple;
import com.simfun.sgf.compat.SingleThreadTaskWorker;
import com.simfun.sgf.net.msg.IMessage;
import com.simfun.sgf.net.msg.InternalMessage;
import com.simfun.sgf.thread.DefaultSimpleFuture;
import com.simfun.sgf.thread.SimpleFuture;
import com.simfun.sgf.utils.JavaUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 主业务worker
 *
 * <AUTHOR>
 */
public class MainWorker extends SingleThreadTaskWorker<Object> {
    public static final String MAIN_WORKER_THREAD_NAME = "MainWorker";
    private static final Logger logger = LoggerFactory.getLogger(MainWorker.class);
    public static final int SHUTDOWN_WAIT_TIME = 5000;
    // private int tickIndex = 0;

    private static final AtomicInteger mainWorkerQueueSize = new AtomicInteger();
    private static final ConcurrentMap<String, AtomicInteger> queueSizeByClass = new ConcurrentHashMap<>();
    private static String currentTaskInfo = "";

    public static int getMainWorkerQueueSize() {
        return mainWorkerQueueSize.get();
    }

    public static void increaseMainWorkerQueueSize(String msgName) {
        queueSizeByClass.computeIfAbsent(msgName, k -> new AtomicInteger(0));
        AtomicInteger u = queueSizeByClass.get(msgName);
        u.incrementAndGet();
        long queueSize = mainWorkerQueueSize.incrementAndGet();
        if (queueSize > 300) {
            List<TwoTuple<String, Integer>> queueStats = new ArrayList<>();
            queueSizeByClass.forEach((k, v) -> {
                int time = v.get();
                if (time > 0) {
                    queueStats.add(new TwoTuple<>(k, time));
                }
            });
            queueStats.sort((t1, t2) -> t1.getSecond() - t2.getSecond());
            long now = System.currentTimeMillis();
            SlowLogUtil.mainWorkerQueueLog(queueSize);
            logger.warn("MainWorker queueSize [{}] too long, {}", queueSize, queueStats);
            logger.warn("MainWorker queueSize {}, current {}", queueSize, currentTaskInfo);
            // logger.info("put main worker msg is {}", msgName);
        }
    }

    private void decreaseMainWorkerQueueSize(String msgName) {
        AtomicInteger counter = queueSizeByClass.get(msgName);
        if (counter != null) {
            counter.decrementAndGet();
        }
        mainWorkerQueueSize.decrementAndGet();
    }

    @Autowired
    private GlobalMessageExecutor msgExec;

    public MainWorker() {
        super(MAIN_WORKER_THREAD_NAME);
    }


    public void put(IMessage msg) {
        increaseMainWorkerQueueSize(msg.getName());
        addTask(msg);
    }

    void putWorldWorker(WorldWorker w) {
        increaseMainWorkerQueueSize(w.getClass().getName());
        addTask(w);
    }


    @Override
    protected void execute(Object task, Object... attach) {
        if (task instanceof WorldWorker) {
            WorldWorker worldWorker = (WorldWorker) task;
            String name = worldWorker.getClass().getName();
            currentTaskInfo = name;
            decreaseMainWorkerQueueSize(name);
            worldWorker.run();
        } else {
            IMessage msg = (IMessage) task;
            String name = msg.getName();
            currentTaskInfo = name;
            decreaseMainWorkerQueueSize(name);
            msgExec.execute(msg);
        }
    }

    @Override
    public void stop() {
        super.stop();
    }

    public void onError(Throwable e) {
        ErrorLogUtil.exceptionLog("MainWorker执行报错", e);
    }

    public <V> V waitMainWorkerResult(Callable<V> callable, long waitMS, String logTag) {
        final SimpleFuture<V> future = new DefaultSimpleFuture<>();
        // 放入mainWorker中执行
        put(new InternalMessage() {

            @Override
            public void execute() {
                long now = TimeUtil.getNow();
                try {
                    V ret = callable.call();
                    future.setSuccess(ret);
                } catch (Throwable e) {
                    future.setFailure(e);
                }
                long use = TimeUtil.getNow() - now;
                if (use > 100) {
                    ErrorLogUtil.errorLog("execute use time is longer than 100ms", "logTag",logTag, "耗时",use);
                }
            }
        });
        // 等待执行结果
        boolean await;
        try {
            await = future.await(waitMS);
        } catch (InterruptedException e) {
            throw new BizException("execute is interrupted","logTag",logTag);
        }
        if (!await) { // timeout
            throw new BizException("execute is timeout","logTag",logTag);
        }
        if (future.isSuccess()) {
            return future.tryGet();
        } else { // 不会cancel，前面的逻辑都没有调用future的cancel，所以只能是发生异常了
            throw JavaUtils.sneakyThrow(future.getCause());
        }
    }

    public static boolean isInMainWorker() {
        return Thread.currentThread().getName().equals(MAIN_WORKER_THREAD_NAME);
    }
}
