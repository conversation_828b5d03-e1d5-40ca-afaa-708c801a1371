package com.simfun.sgf.compat;

/**
 * 队列状态枚举
 */
public enum QueueState {
    CREATED("Queue created but not started"),
    STARTING("Queue is starting"),
    RUNNING("Queue is running normally"),
    STOPPING("Queue is stopping"),
    STOPPED("Queue has been stopped"),
    ERROR("Queue encountered an error");

    private final String description;

    QueueState(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public boolean isActive() {
        return this == RUNNING || this == STARTING;
    }

    public boolean canAcceptTasks() {
        return this == RUNNING;
    }
} 