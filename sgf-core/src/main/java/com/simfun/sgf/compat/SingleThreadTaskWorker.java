package com.simfun.sgf.compat;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 单线程任务工作器
 *
 * @param <T> 任务类型
 */
public abstract class SingleThreadTaskWorker<T> implements AutoCloseable {

    protected static final Logger log = LoggerFactory.getLogger(SingleThreadTaskWorker.class);

    private final AtomicReference<WorkerState> state = new AtomicReference<>(WorkerState.INIT);
    private final WorkerConfig config;
    private volatile MessageQueue<T> messageQueue;

    protected SingleThreadTaskWorker() {
        this.config = WorkerConfig.defaultConfig(null);
    }

    protected SingleThreadTaskWorker(String name) {
        this.config = WorkerConfig.defaultConfig(name);
    }

    protected SingleThreadTaskWorker(WorkerConfig config) {
        this.config = config;
    }

    /**
     * 执行任务
     *
     * @param task   任务对象
     * @param attach 附加参数
     */
    protected abstract void execute(T task, Object... attach);

    /**
     * 启动工作器
     */
    public void start() {
        if (!state.compareAndSet(WorkerState.INIT, WorkerState.START)) {
            throw new IllegalStateException("Worker state is illegal. expected=" + WorkerState.INIT + ", actual=" + state.get());
        }

        try {
            this.messageQueue = TaskWorkers.<T>multiProducer()
                    .name(config.name())
                    .bufferSize(config.bufferSize())
                    .processor(this::execute)
                    .threadFactory(config.threadFactory().orElse(null))
                    .build();

            messageQueue.start();

            if (log.isInfoEnabled()) {
                log.info("{} is started", config.name());
            }
        } catch (Exception e) {
            state.set(WorkerState.INIT);
            throw new RuntimeException("Failed to start worker", e);
        }
    }

    /**
     * 停止工作器
     */
    public void stop() {
        if (!state.compareAndSet(WorkerState.START, WorkerState.STOP)) {
            return; // Already stopped or not started
        }

        try {
            if (messageQueue != null) {
                messageQueue.shutdown(config.shutdownTimeout());
            }

            if (log.isInfoEnabled()) {
                log.info("{} is stopped", config.name());
            }
        } catch (Exception e) {
            log.error("Failed to stop {}", config.name(), e);
        }
    }

    /**
     * 添加任务
     *
     * @param task 任务
     */
    protected void addTask(T task) {
        if (!state.get().canAcceptTasks()) {
            throw new RejectedExecutionException("Worker has already been shutdown");
        }

        if (messageQueue != null) {
            messageQueue.submit(task);
        }
    }

    /**
     * 获取任务队列大小
     *
     * @return 任务数量
     */
    public long getTaskSize() {
        if (messageQueue != null) {
            var metrics = messageQueue.getMetrics();
            return metrics.pendingTasks();
        }
        return 0;
    }

    /**
     * AutoCloseable实现
     */
    @Override
    public void close() {
        stop();
    }

}