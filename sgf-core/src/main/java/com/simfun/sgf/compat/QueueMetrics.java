package com.simfun.sgf.compat;

/**
 * 队列运行指标
 *
 * @param totalProduced     总生产数量
 * @param totalConsumed     总消费数量
 * @param remainingCapacity 剩余容量
 * @param currentState      当前状态
 */
public record QueueMetrics(
        long totalProduced,
        long totalConsumed,
        long remainingCapacity,
        QueueState currentState
) {

    /**
     * 获取待处理任务数量
     *
     * @return 待处理数量
     */
    public long pendingTasks() {
        return totalProduced - totalConsumed;
    }

    /**
     * 队列是否空闲
     *
     * @return true if no pending tasks
     */
    public boolean isEmpty() {
        return pendingTasks() == 0;
    }

    /**
     * 创建空指标
     *
     * @return 初始指标
     */
    public static QueueMetrics empty() {
        return new QueueMetrics(0, 0, 0, QueueState.CREATED);
    }

}