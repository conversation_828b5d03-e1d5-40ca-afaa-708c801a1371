package com.simfun.sgf.compat;

import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.ThreadFactory;

/**
 * 工作器配置
 *
 * @param name            工作器名称
 * @param bufferSize      缓冲区大小
 * @param shutdownTimeout 关闭超时时间
 * @param threadFactory   线程工厂
 */
public record WorkerConfig(
        String name,
        int bufferSize,
        Duration shutdownTimeout,
        Optional<ThreadFactory> threadFactory
) {

    private static final int DEFAULT_BUFFER_SIZE = 1 << 20; // 1M
    private static final Duration DEFAULT_SHUTDOWN_TIMEOUT = Duration.ofSeconds(5);

    /**
     * 创建默认配置
     *
     * @return 默认配置
     */
    public static WorkerConfig defaultConfig(String name) {
        return new WorkerConfig(
                name != null ? name : "SingleThreadTaskWorker",
                DEFAULT_BUFFER_SIZE,
                DEFAULT_SHUTDOWN_TIMEOUT,
                Optional.empty()
        );
    }

    /**
     * 获取关闭超时毫秒数
     *
     * @return 毫秒数
     */
    public int getShutdownWaitTime() {
        return (int) shutdownTimeout.toMillis();
    }
} 