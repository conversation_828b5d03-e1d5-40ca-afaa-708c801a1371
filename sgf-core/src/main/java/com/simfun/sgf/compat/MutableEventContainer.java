package com.simfun.sgf.compat;

/**
 * 可变的事件容器，专用于Disruptor的RingBuffer
 *
 * @param <T> 事件数据类型
 */
public final class MutableEventContainer<T> {

    private T value;

    /**
     * 默认构造函数
     */
    public MutableEventContainer() {
        this.value = null;
    }

    /**
     * 获取值
     *
     * @return 当前值
     */
    public T getValue() {
        return value;
    }

    /**
     * 设置值
     *
     * @param value 新值
     */
    public void setValue(T value) {
        this.value = value;
    }

    /**
     * 获取值并清空
     *
     * @return 当前值
     */
    public T getAndClear() {
        T current = this.value;
        this.value = null;
        return current;
    }

    @Override
    public String toString() {
        return "MutableEventContainer{value=" + value + '}';
    }
} 