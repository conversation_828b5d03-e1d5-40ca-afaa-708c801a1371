package com.simfun.sgf.thread4;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.time.Duration;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 现代化的单线程任务工作器（兼容旧API）
 * 使用方式与 SingleThreadDisruptorTaskWorker 完全相同
 * 
 * @param <T> 任务类型
 * <AUTHOR>
 */
public abstract class SingleThreadTaskWorker<T> implements AutoCloseable {
    
    protected static final Logger log = LoggerFactory.getLogger(SingleThreadTaskWorker.class);
    
    private final AtomicReference<WorkerState> state = new AtomicReference<>(WorkerState.INIT);
    private final WorkerConfig config;
    private volatile MessageQueue<T> messageQueue;
    
    /**
     * 受保护的构造函数，供Builder调用
     */
    protected SingleThreadTaskWorker() {
        this.config = WorkerConfig.defaultConfig();
    }
    
    /**
     * 受保护的构造函数，指定配置
     */
    protected SingleThreadTaskWorker(WorkerConfig config) {
        this.config = config;
    }
    
    /**
     * 执行任务
     * 
     * @param task 任务对象
     * @param attach 附加参数
     */
    protected abstract void execute(T task, Object... attach);
    
    /**
     * 启动工作器
     */
    public void start() {
        if (!state.compareAndSet(WorkerState.INIT, WorkerState.START)) {
            throw new IllegalStateException("Worker state is illegal. expected=" + WorkerState.INIT + ", actual=" + state.get());
        }
        
        try {
            this.messageQueue = TaskWorkers.<T>multiProducer()
                .name(config.name())
                .bufferSize(config.bufferSize())
                .processor(this::execute)
                .threadFactory(config.threadFactory().orElse(null))
                .build();
            
            messageQueue.start();
            
            if (log.isInfoEnabled()) {
                log.info("{} is started", config.name());
            }
        } catch (Exception e) {
            state.set(WorkerState.INIT);
            throw new RuntimeException("Failed to start worker", e);
        }
    }
    
    /**
     * 停止工作器
     */
    public void stop() {
        if (!state.compareAndSet(WorkerState.START, WorkerState.STOP)) {
            return; // Already stopped or not started
        }
        
        try {
            if (messageQueue != null) {
                messageQueue.shutdown(config.shutdownTimeout());
            }
            
            if (log.isInfoEnabled()) {
                log.info("{} is stopped", config.name());
            }
        } catch (Exception e) {
            log.error("Failed to stop {}", config.name(), e);
        }
    }
    
    /**
     * 添加任务（兼容旧API方法名）
     * 
     * @param task 任务
     */
    public void addTask(T task) {
        if (!state.get().canAcceptTasks()) {
            throw new RejectedExecutionException("Worker has already been shutdown");
        }
        
        if (messageQueue != null) {
            messageQueue.submit(task);
        }
    }
    
    /**
     * 获取任务队列大小（兼容旧API）
     * 
     * @return 任务数量
     */
    public long getTaskSize() {
        if (messageQueue != null) {
            var metrics = messageQueue.getMetrics();
            return metrics.pendingTasks();
        }
        return 0;
    }
    
    /**
     * AutoCloseable实现
     */
    @Override
    public void close() {
        stop();
    }
    
    /**
     * 创建Builder（与旧版本API完全相同）
     * 
     * @param workerClass 工作器类
     * @param <T> 任务类型
     * @return Builder实例
     */
    public static <T> Builder<T> newBuilder(Class<? extends SingleThreadTaskWorker<T>> workerClass) {
        return new Builder<>(workerClass);
    }
    
    /**
     * Builder类（与旧版本API兼容）
     * 
     * @param <T> 任务类型
     */
    public static class Builder<T> {
        private final Class<? extends SingleThreadTaskWorker<T>> workerClass;
        private String name;
        private int shutdownWaitTime;
        private int bufferSize;
        private ThreadFactory threadFactory;
        
        private Builder(Class<? extends SingleThreadTaskWorker<T>> workerClass) {
            this.workerClass = workerClass;
        }
        
        public Builder<T> setName(String name) {
            this.name = name;
            return this;
        }
        
        public Builder<T> setShutdownWaitTime(int shutdownWaitTime) {
            this.shutdownWaitTime = shutdownWaitTime;
            return this;
        }
        
        public Builder<T> setBufferSize(int bufferSize) {
            this.bufferSize = bufferSize;
            return this;
        }
        
        public Builder<T> setThreadFactory(ThreadFactory threadFactory) {
            this.threadFactory = threadFactory;
            return this;
        }
        
        @SuppressWarnings("unchecked")
        public <W extends SingleThreadTaskWorker<T>> W build() {
            try {
                // 设置默认值
                if (name == null || name.isEmpty()) {
                    name = workerClass.getSimpleName();
                }
                
                if (shutdownWaitTime <= 0) {
                    shutdownWaitTime = 5000; // 默认5秒
                }
                
                if (bufferSize <= 0) {
                    bufferSize = 1 << 20; // 默认1M
                }
                
                // 创建配置
                var config = new WorkerConfig(
                    name,
                    bufferSize,
                    Duration.ofMillis(shutdownWaitTime),
                    java.util.Optional.ofNullable(threadFactory)
                );
                
                // 使用反射创建实例（与旧版本相同方式）
                Constructor<? extends SingleThreadTaskWorker<T>> constructor = 
                    workerClass.getDeclaredConstructor();
                constructor.setAccessible(true);
                SingleThreadTaskWorker<T> worker = constructor.newInstance();
                
                // 返回配置包装器
                return (W) new ConfiguredWorker<>(worker, config, workerClass);
                
            } catch (Throwable t) {
                log.error("创建单线程队列异常！！", t);
                return null;
            }
        }
    }
    
    /**
     * 内部配置包装类
     */
    private static class ConfiguredWorker<T> extends SingleThreadTaskWorker<T> {
        private final SingleThreadTaskWorker<T> delegate;
        private final Class<? extends SingleThreadTaskWorker<T>> workerClass;
        
        ConfiguredWorker(SingleThreadTaskWorker<T> delegate, WorkerConfig config, 
                        Class<? extends SingleThreadTaskWorker<T>> workerClass) {
            super(config);
            this.delegate = delegate;
            this.workerClass = workerClass;
        }
        
        @Override
        protected void execute(T task, Object... attach) {
            delegate.execute(task, attach);
        }
        
        @Override
        public String toString() {
            return workerClass.getSimpleName() + "{" + super.toString() + "}";
        }
    }
} 