package com.simfun.sgf.thread4;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.SleepingWaitStrategy;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.YieldingWaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;

import java.time.Duration;
import java.util.concurrent.ThreadFactory;

/**
 * 任务工作器工厂类，提供便捷的创建方法（方案3：统一Builder设计）
 * 
 * <AUTHOR>
 */
public final class TaskWorkers {
    
    private TaskWorkers() {
        // 工具类，禁止实例化
    }
    
    /**
     * 创建任务队列构建器（单一生产者）
     * 单一生产者适用于只有一个线程向队列提交任务的场景，性能更好
     * 
     * @param <T> 任务类型
     * @return 构建器
     */
    public static <T> QueueBuilder<T> singleProducer() {
        return new QueueBuilder<>(ProducerType.SINGLE);
    }
    
    /**
     * 创建任务队列构建器（多个生产者）
     * 多个生产者适用于多个线程并发向队列提交任务的场景
     * 
     * @param <T> 任务类型
     * @return 构建器
     */
    public static <T> QueueBuilder<T> multiProducer() {
        return new QueueBuilder<>(ProducerType.MULTI);
    }
    
    /**
     * 为了向后兼容保留的方法（建议使用singleProducer()）
     * 
     * @deprecated 使用 {@link #singleProducer()} 替代
     */
    @Deprecated(since = "2.0", forRemoval = false)
    public static <T> QueueBuilder<T> singleThread() {
        return singleProducer();
    }
    
    /**
     * 为了向后兼容保留的方法（建议使用multiProducer()）
     * 
     * @deprecated 使用 {@link #multiProducer()} 替代
     */
    @Deprecated(since = "2.0", forRemoval = false)
    public static <T> QueueBuilder<T> multiThread() {
        return multiProducer();
    }
    
    /**
     * 快速创建单一生产者处理器并处理任务
     * 
     * @param name 处理器名称
     * @param processor 处理逻辑
     * @param tasks 任务列表
     * @param <T> 任务类型
     */
    @SafeVarargs
    public static <T> void processInSingleProducer(String name, TaskProcessor<T> processor, T... tasks) {
        try (var worker = TaskWorkers.<T>singleProducer()
                .name(name)
                .processor(processor)
                .build()) {
            
            worker.start();
            for (T task : tasks) {
                worker.submit(task);
            }
            
            // 等待处理完成
            while (worker.getMetrics().pendingTasks() > 0) {
                Thread.onSpinWait();
            }
        }
    }
    
    /**
     * 统一的队列构建器
     * 
     * @param <T> 任务类型
     */
    public static class QueueBuilder<T> {
        private final ProducerType producerType;
        private String name;
        private int bufferSize = 1 << 16;
        private TaskProcessor<T> processor = null;
        private Duration shutdownTimeout = Duration.ofSeconds(5);
        private ThreadFactory threadFactory;
        private WaitStrategy waitStrategy = new BlockingWaitStrategy();
        
        QueueBuilder(ProducerType producerType) {
            this.producerType = producerType;
            this.name = producerType == ProducerType.SINGLE ? 
                "single-producer-worker" : "multi-producer-worker";
        }
        
        /**
         * 设置工作器名称
         */
        public QueueBuilder<T> name(String name) {
            this.name = name;
            return this;
        }
        
        /**
         * 设置缓冲区大小（必须是2的幂）
         */
        public QueueBuilder<T> bufferSize(int bufferSize) {
            this.bufferSize = bufferSize;
            return this;
        }

        /**
         * 设置任务处理器
         */
        public QueueBuilder<T> processor(TaskProcessor<T> processor) {
            this.processor = processor;
            return this;
        }
        
        /**
         * 设置关闭超时时间
         */
        public QueueBuilder<T> shutdownTimeout(Duration timeout) {
            this.shutdownTimeout = timeout;
            return this;
        }
        
        /**
         * 设置线程工厂
         */
        public QueueBuilder<T> threadFactory(ThreadFactory threadFactory) {
            this.threadFactory = threadFactory;
            return this;
        }
        
        /**
         * 使用阻塞等待策略（默认）
         * 适合大多数场景，CPU友好
         */
        public QueueBuilder<T> blockingWait() {
            this.waitStrategy = new BlockingWaitStrategy();
            return this;
        }
        
        /**
         * 使用自旋等待策略
         * 低延迟但高CPU使用率，适合延迟敏感的场景
         */
        public QueueBuilder<T> yieldingWait() {
            this.waitStrategy = new YieldingWaitStrategy();
            return this;
        }
        
        /**
         * 使用睡眠等待策略
         * 平衡延迟和CPU使用率
         */
        public QueueBuilder<T> sleepingWait() {
            this.waitStrategy = new SleepingWaitStrategy();
            return this;
        }
        
        /**
         * 自定义等待策略
         */
        public QueueBuilder<T> waitStrategy(WaitStrategy waitStrategy) {
            this.waitStrategy = waitStrategy;
            return this;
        }
        
        /**
         * 构建消息队列
         */
        public MessageQueue<T> build() {
            TaskProcessor<T> actualProcessor = processor != null ? processor : TaskProcessor.<T>noOp();
            var config = QueueConfig.<T>builder()
                .bufferSize(bufferSize)
                .processor(actualProcessor)
                .producerType(producerType)
                .name(name)
                .shutdownTimeout(shutdownTimeout)
                .threadFactory(threadFactory)
                .waitStrategy(waitStrategy)
                .build();
                
            return new DisruptorMessageQueue<>(config);
        }
    }
} 